import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:logger/logger.dart';

import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';

/// Repository for managing homework data with Firebase Firestore
class HomeworkRepository {
  static final HomeworkRepository _instance = HomeworkRepository._internal();
  factory HomeworkRepository() => _instance;
  HomeworkRepository._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Logger _logger = Logger();

  // Collection names
  static const String _homeworkCollection = 'homeworks';
  static const String _submissionsCollection = 'homework_submissions';
  static const String _statusCollection = 'homework_status';

  /// Fetch all homework assigned for a specific date
  Future<List<HomeworkModel>> getHomeworkForDate(DateTime date) async {
    try {
      _logger.i('Fetching homework for date: ${date.toIso8601String()}');

      // Create start and end of day timestamps for the query
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where(
            'assignedAt',
            isGreaterThanOrEqualTo: startOfDay.toIso8601String(),
          )
          .where('assignedAt', isLessThanOrEqualTo: endOfDay.toIso8601String())
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for date',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework for date: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework for date: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch a single homework by document ID
  Future<HomeworkModel?> getHomeworkById(String homeworkId) async {
    try {
      _logger.i('Fetching homework by ID: $homeworkId');

      final docSnapshot = await _firestore
          .collection(_homeworkCollection)
          .doc(homeworkId)
          .get();

      if (!docSnapshot.exists) {
        _logger.w('Homework not found with ID: $homeworkId');
        return null;
      }

      final data = docSnapshot.data()!;
      data['id'] = docSnapshot.id; // Ensure the document ID is included

      final homework = HomeworkModel.fromJson(data);
      _logger.i('Successfully fetched homework: $homeworkId');
      return homework;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework by ID: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework by ID: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Fetch a student's submission for a given homework
  Future<HomeworkSubmissionModel?> getSubmission(
    String homeworkId,
    String userId,
  ) async {
    try {
      final submissionId = '${homeworkId}_$userId';
      _logger.i('Fetching submission: $submissionId');

      final docSnapshot = await _firestore
          .collection(_submissionsCollection)
          .doc(submissionId)
          .get();

      if (!docSnapshot.exists) {
        _logger.i('No submission found for: $submissionId');
        return null;
      }

      final submission = HomeworkSubmissionModel.fromJson(docSnapshot.data()!);
      _logger.i('Successfully fetched submission: $submissionId');
      return submission;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching submission: ${e.message}');
      throw Exception('Failed to fetch submission: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching submission: $e');
      throw Exception('Failed to fetch submission: $e');
    }
  }

  /// Create or update a homework submission
  Future<void> submitHomework(HomeworkSubmissionModel submission) async {
    try {
      final submissionId = '${submission.homeworkId}_${submission.userId}';
      _logger.i('Submitting homework: $submissionId');

      await _firestore
          .collection(_submissionsCollection)
          .doc(submissionId)
          .set(submission.toJson());

      _logger.i('Successfully submitted homework: $submissionId');
    } on FirebaseException catch (e) {
      _logger.e('Firebase error submitting homework: ${e.message}');
      throw Exception('Failed to submit homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error submitting homework: $e');
      throw Exception('Failed to submit homework: $e');
    }
  }

  /// Mark homework as done/undone for homework that doesn't require submission
  Future<void> markHomeworkDone(
    String homeworkId,
    String userId,
    bool isDone,
  ) async {
    try {
      final statusId = '${homeworkId}_$userId';
      _logger.i('Marking homework ${isDone ? 'done' : 'undone'}: $statusId');

      await _firestore.collection(_statusCollection).doc(statusId).set({
        'homeworkId': homeworkId,
        'userId': userId,
        'isDone': isDone,
        'updatedAt': DateTime.now().toIso8601String(),
      });

      _logger.i(
        'Successfully marked homework ${isDone ? 'done' : 'undone'}: $statusId',
      );
    } on FirebaseException catch (e) {
      _logger.e('Firebase error marking homework done: ${e.message}');
      throw Exception('Failed to mark homework done: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error marking homework done: $e');
      throw Exception('Failed to mark homework done: $e');
    }
  }

  /// Get homework status (done/undone) for homework that doesn't require submission
  Future<bool> getHomeworkStatus(String homeworkId, String userId) async {
    try {
      final statusId = '${homeworkId}_$userId';
      _logger.i('Fetching homework status: $statusId');

      final docSnapshot = await _firestore
          .collection(_statusCollection)
          .doc(statusId)
          .get();

      if (!docSnapshot.exists) {
        _logger.i(
          'No status found for homework: $statusId, defaulting to false',
        );
        return false;
      }

      final data = docSnapshot.data()!;
      final isDone = data['isDone'] as bool? ?? false;
      _logger.i('Successfully fetched homework status: $statusId = $isDone');
      return isDone;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching homework status: ${e.message}');
      throw Exception('Failed to fetch homework status: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework status: $e');
      throw Exception('Failed to fetch homework status: $e');
    }
  }

  /// Get all homework for a date range (useful for date selector)
  Future<List<HomeworkModel>> getHomeworkForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      _logger.i(
        'Fetching homework for date range: ${startDate.toIso8601String()} to ${endDate.toIso8601String()}',
      );

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .where(
            'assignedAt',
            isGreaterThanOrEqualTo: startDate.toIso8601String(),
          )
          .where('assignedAt', isLessThanOrEqualTo: endDate.toIso8601String())
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for date range',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e(
        'Firebase error fetching homework for date range: ${e.message}',
      );
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching homework for date range: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }

  /// Get all homework (useful for "All" filter)
  Future<List<HomeworkModel>> getAllHomework() async {
    try {
      _logger.i('Fetching all homework');

      final querySnapshot = await _firestore
          .collection(_homeworkCollection)
          .orderBy('assignedAt', descending: true)
          .get();

      final homeworkList = querySnapshot.docs
          .map((doc) {
            try {
              final data = doc.data();
              data['id'] = doc.id; // Ensure the document ID is included
              return HomeworkModel.fromJson(data);
            } catch (e) {
              _logger.e('Error parsing homework document ${doc.id}: $e');
              return null;
            }
          })
          .where((homework) => homework != null)
          .cast<HomeworkModel>()
          .toList();

      _logger.i(
        'Successfully fetched ${homeworkList.length} total homework items',
      );
      return homeworkList;
    } on FirebaseException catch (e) {
      _logger.e('Firebase error fetching all homework: ${e.message}');
      throw Exception('Failed to fetch homework: ${e.message}');
    } catch (e) {
      _logger.e('Unexpected error fetching all homework: $e');
      throw Exception('Failed to fetch homework: $e');
    }
  }
}
